import { MainMap } from '@/components/map/MainMap'
import { GeoMeta, TimeLineItem, Way } from '@/types'
import {
  Button,
  Chip,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownTrigger,
  Input,
  Modal,
  ModalBody,
  ModalContent,
  <PERSON>dal<PERSON><PERSON>er,
  <PERSON>dal<PERSON>eader,
  Spinner,
  Textarea,
  useDisclosure
} from "@heroui/react"
import { useCallback, useEffect, useMemo, useState } from 'react'

import dayjs from 'dayjs'
import { SearchIcon } from '@/lib/svg/SearchIcon'
import { trpc } from '@/trpc'
import { toast } from 'react-toastify'
import { getShortPointName } from '@/lib/utils/getShortPointName'
import { CDatePicker } from '@/components/DatePicker'
import CalendarIcon from '@/lib/svg/CalendarIcon'
import { useMatch } from '@tanstack/react-router'
import { useTranslation } from 'react-i18next'
import { ProfileContainer } from '@/components/ProfileContainer'
import { useMediaQuery } from '@uidotdev/usehooks'
import { statusList } from '@/lib/utils/statusList'
import { statusColorByName } from '@/lib/utils/statusColor'
import IconPencil from '@/lib/svg/IconPencil'
import { useRouter } from '@tanstack/react-router'

type PointType = 'from' | 'to' | 'middlepoints'

function geometaToWay(data: GeoMeta) {
  const point: Way = {
    lat: data?.lat,
    lon: data?.lon,
    geometa: data
  }

  return point
}

export const CreateCasePage = () => {
  // const router = useRouter()
  const { t } = useTranslation()
  const route = useMatch({ from: '/create' })
  const { navigate } = useRouter()

  const isSmallDevice = useMediaQuery('only screen and (max-width : 600px)')

  const { isOpen, onOpen, onOpenChange } = useDisclosure()
  const [activePointType, setActivePointType] = useState<PointType>('from')
  const [activeMiddlePoint, setActiveMiddlePoint] = useState<Partial<Way>>()
  const [price, setPrice] = useState('')
  const [description, setDescription] = useState('')
  const [isRequest, setIsRequest] = useState<boolean | undefined>()

  const [isEdit, setIsEdit] = useState<boolean | undefined>()
  const [statusValue, setStatusValue] = useState(new Set([]))
  const stringStatusValue = useMemo(() => Array.from(statusValue).join(', ').replace('_', ' '), [statusValue])

  const {
    isFetching: caseData_isFetching,
    data: caseData,
    isError: caseData_isError,
    error: caseData_error
  } = trpc.case.id.useQuery(route.search?.caseId!, {
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
    enabled: !!(isEdit && route.search?.caseId)
  })

  useEffect(() => {
    setIsEdit(!!route.search?.caseId)
    setIsRequest(!!route.search.isRequest)
  }, [])

  useEffect(() => {
    if (caseData?.id) {
      setFromPoint(caseData.from)
      setToPoint(caseData.to)

      caseData.middlepoints?.length && setMiddlePoints(caseData.middlepoints)

      setPrice(caseData.price || '')
      setDescription(caseData.description ?? '')

      setStatusValue(new Set([caseData.status]))
    } else {
      setFromPoint({
        date: dayjs(new Date()).toDate()
      })
      
      setToPoint({
        date: dayjs(new Date()).add(2, 'days').toDate()
      })

      setMiddlePoints([])

      setPrice('')
      setDescription('')
      setStatusValue(new Set(['OPEN']))
    }
  }, [caseData])

  const [fromPoint, setFromPoint] = useState<Partial<Way>>(
    route.search.from
      ? geometaToWay(route.search.from)
      : {
          date: dayjs(new Date()).toDate()
        }
  )

  const [toPoint, setToPoint] = useState<Partial<Way>>(
    route.search.to
      ? geometaToWay(route.search.to)
      : {
          date: dayjs(new Date()).add(2, 'days').toDate()
        }
  )

  const [middlePoints, setMiddlePoints] = useState<Partial<Way>[]>([])

  // useEffect(() => {
  //   route.search.from && setFromPoint(route.search.from)
  // }, [])

  const pointSetters = {
    from: setFromPoint,
    to: setToPoint,
    middlepoints: setMiddlePoints
  }

  const pointGetters = {
    from: fromPoint,
    to: toPoint,
    middlepoints: middlePoints
  }

  const { isPending: isLoading, isError, isSuccess, error, mutateAsync, data: createdCase } = trpc.case.updateOrCreate.useMutation()

  useEffect(() => {
    if (isError) {
      toast.error(`${t('Something went wrong')}:  ${String(error.shape?.code)}`)
    }
  }, [error, isError])

  useEffect(() => {
    if (caseData_isError) {
      toast.error(`${t('Something went wrong')}:  ${String(caseData_error.shape?.code)}`)

      //history.back()
      navigate({
        to: '/favorites'
      })
    }
  }, [caseData_isError, caseData_error])

  useEffect(() => {
    if (isSuccess) {
      toast.success(`${t('Success')}`)
      navigate({
        to: '/route/$caseId',
        params: {
          caseId: createdCase?.id
        }
      })
    }
  }, [isSuccess])

  async function createCase() {
    await mutateAsync({
      middlepoints: middlePoints.map((i) => {
        delete i._id
        return i
      }),
      from: fromPoint,
      to: toPoint,
      isRequest,
      // expire_at:
      price: Number(Number(price).toFixed(2)) || 0,
      description,
      status: stringStatusValue || undefined,
      id: route.search?.caseId || undefined
    })
  }

  function searchModalHandler(pointType: PointType, point?: Way) {
    if (point?.lat) {
      setActiveMiddlePoint(point)
    }
    setActivePointType(pointType)
    onOpen()
  }

  function onSearchInputChange(data: GeoMeta) {
    const point: Way = {
      geometa: data,
      lat: data.lat,
      lon: data.lon
      // date: dayjs(new Date()).toDate()
    }

    if (activePointType === 'middlepoints') {
      addNewPoint(point)
    } else {
      updatePoint({
        type: activePointType,
        values: {
          ...point
        }
      })
    }
  }

  const updatePoint = useCallback(({ type, values }: { type: PointType; values: { [K in keyof Way]?: Way[K] } }) => {
    pointSetters[type]((prevValue: any) => ({
      ...prevValue,
      ...values
    }))
  }, [])
  const updatePointField = useCallback(({ type, field, value, point }: { type: PointType; field: keyof Way; value: string; point: Way }) => {
    if (type !== 'middlepoints') {
      pointSetters[type]((prevValue) => ({
        ...prevValue,
        [field]: value
      }))
    } else {
      pointSetters[type]((prevValue) => {
        let f = prevValue.find((x) => x._id == point._id)
        f && (f[field] = value)

        return prevValue
      })
    }
  }, [])

  const Comment = ({ type, point }: { type: PointType; point: Way }) => {
    const [value, setValue] = useState(point?.comment)
    return (
      <div>
        <Textarea
          value={value}
          // ref={ref}
          onValueChange={setValue}
          onBlur={() => {
            updatePointField({ field: 'comment', type, value: value || '', point })
          }}
          label={t('Note')}
          labelPlacement='inside'
          placeholder={t('Enter your comment for this location')}
          className='max-w-xs'
        />
      </div>
    )
  }

  function deletePointHandler(point: Way): void {
    console.log('delete handler:', point)

    setMiddlePoints((prevState) => prevState.filter((i) => (i._id ?? i.id) !== (point._id ?? point.id)))
  }

  useEffect(() => console.log('fromPoint:', fromPoint), [fromPoint])

  const DatePicker = ({ type, point }: { type: PointType; point: Way }) => {
    const [currentDate, setCurrentDate] = useState<Date>(point?.date ?? new Date())

    // useEffect(() => console.log('currentDate:', currentDate), [currentDate])

    const handleChange = (selectedDate: any) => {
      const date = selectedDate ? new Date(selectedDate.toString()) : new Date()
      setCurrentDate(date)
      // updatePoint({
      //   type,
      //   values: {
      //     date: currentDate
      //   }
      // })
      updatePointField({
        type,
        field: 'date',
        point,
        value: date
      })
    }

    return (
      <div>
        <CDatePicker
          // popoverProps={{
          //   placement: 'right-start'
          // }}
          calendarProps={{
            // showButtonBar: true,
            // selectionMode: 'single',
            value: currentDate,
            onChange: (e) => handleChange(e.value)
            // onClearButtonClick: onClearDates
          }}
          inputProps={{
            startContent: <div className='text-sm'>{t('Date')}</div>,
            endContent: <CalendarIcon />,
            classNames: {
              input: 'text-center'
            },
            variant: 'flat',
            className: 'font-semibold sm:max-w-[340px]',
            color: 'default'
          }}
        />
      </div>
    )
  }

  const PointBody = ({ point, type }: { point: Way; type: PointType }) => {
    return (
      <div className='cursor-pointer flex gap-3 items-center'>
        {point?.geometa?.display_name ? (
          <div>
            {/* <h3 className='text-lg'>{point.geometa?.address?.country}, {point.geometa?.address.city}</h3> */}
            <div className='text-zinc-500 dark:text-zinc-400' onClick={() => searchModalHandler(type, point)}>
              {point.geometa?.display_name}
            </div>
            {/* <div>{point.geometa?.address?.country && `${toPoint.geometa?.address.country}, ${toPoint.geometa?.address.city}`}</div> */}
            <div className='mt-5 flex space-x-2'>
              <div>{!isRequest && <DatePicker type={type} point={point} />}</div>
              <div>{/* <Timepicker /> */}</div>
            </div>
            <div className='mt-5'>{!isRequest && <Comment type={type} point={point} />}</div>
          </div>
        ) : (
          <Button
            size={isSmallDevice ? 'sm' : 'md'}
            startContent={<SearchIcon />}
            variant='flat'
            className='font-semibold'
            color='default'
            onPress={() => searchModalHandler(type)}
          >
            {t('Click to find location')}
          </Button>
        )}
        {type === 'middlepoints' && (
          <Button onPress={(e) => deletePointHandler(point)} className='font-bold' isIconOnly radius='full' variant='flat' color='danger'>
            X
          </Button>
        )}
      </div>
    )
  }

  const timelineItems: TimeLineItem[] = useMemo(() => {
    return [
      {
        // timelabel: getShortPointName(fromPoint, 'from'),
        timelabel: (
          <Chip
            onClick={() => searchModalHandler('from', fromPoint)}
            radius='sm'
            color='primary'
            classNames={{
              content: 'font-bold text-ellipsis md:text-lg'
            }}
            variant='dot'
          >
            {getShortPointName(fromPoint, 'from')}
          </Chip>
        ),
        body: <PointBody point={fromPoint} type='from' />
        // pointIcon: <div className='w-3 h-3 bg-blue-400 rounded-full'></div>
      },
      ...middlePoints?.map((point) => ({
        // timelabel: getShortPointName(point, 'middlepoints'),
        timelabel: (
          <Chip
            onClick={() => searchModalHandler('to', point)}
            radius='sm'
            color='secondary'
            classNames={{
              content: 'font-bold text-ellipsis md:text-lg'
            }}
            variant='dot'
          >
            {getShortPointName(point, 'middlepoints')}
          </Chip>
        ),
        body: <PointBody point={point} type='middlepoints' />,
        pointIcon: <div className='w-3 h-3 bg-default-300 rounded-full'></div>
      })),
      {
        // timelabel: getShortPointName(toPoint, 'to'),
        timelabel: (
          <Chip
            onClick={() => searchModalHandler('to', toPoint)}
            radius='sm'
            color='secondary'
            classNames={{
              content: 'font-bold text-ellipsis md:text-lg'
            }}
            variant='dot'
          >
            {getShortPointName(toPoint, 'to')}
          </Chip>
        ),
        body: <PointBody point={toPoint} type='to' />
        // pointIcon: <div className='w-3 h-3 bg-green-300 rounded-full'></div>
      }
    ]
  }, [fromPoint, toPoint, middlePoints])

  function addNewPoint(data?: Way) {
    if (middlePoints.find((x) => x.lat == data?.lat)) {
      return false
    }
    setMiddlePoints((prevState) => {
      return [
        ...prevState,
        {
          date: new Date(),
          _id: new Date().getTime(),
          ...data
        }
      ]
    })
  }

  function mapLocationValue() {
    if (activePointType == 'middlepoints') {
      return activeMiddlePoint?.geometa
    }
    return pointGetters[activePointType]?.geometa
  }

  return (
    <ProfileContainer>
      <div>
        <div className='flex justify-center'>{caseData_isFetching && <Spinner className='my-5' />}</div>
        {!caseData_isFetching && (
          <div>
            <div className='text-default-700 font-semibold text-lg'>{isRequest ? t('Subscribe to route') : t('Create new')}</div>
            <div className='mt-5 mb-10'>
              {/*TODO:  toolbar needs refactoring */}
              <div className='flex justify-start flex-wrap items-end space-y-3 space-x-3'>
                <div></div>
                <div>
                  {!isRequest && (
                    <Input
                      type='number'
                      label={t('Price')}
                      size='sm'
                      classNames={{
                        input: 'w-24'
                      }}
                      value={price}
                      onValueChange={setPrice}
                      variant='flat'
                      placeholder='0.00'
                      labelPlacement='inside'
                      startContent={
                        <div className='pointer-events-none flex items-center'>
                          <span className='text-default-400 text-small'>$</span>
                        </div>
                      }
                    />
                  )}
                </div>
                {isEdit && (
                  <Dropdown>
                    <DropdownTrigger>
                      <Button variant='light' color={statusColorByName(stringStatusValue)} className='capitalize'>
                        <span>{stringStatusValue.toLocaleLowerCase()}</span>
                        <IconPencil />
                      </Button>
                    </DropdownTrigger>
                    <DropdownMenu
                      aria-label='Single selection example'
                      variant='flat'
                      disallowEmptySelection
                      selectionMode='single'
                      selectedKeys={statusValue}
                      onSelectionChange={setStatusValue}
                    >
                      {statusList.map((statusText) => (
                        <DropdownItem key={statusText}>
                          <Chip variant='light' color={statusColorByName(statusText)} className='capitalize'>
                            {statusText.toLocaleLowerCase()}
                          </Chip>
                        </DropdownItem>
                      ))}
                    </DropdownMenu>
                  </Dropdown>
                )}

                <Button className='text-ellipsis' size={isSmallDevice ? 'sm' : 'md'} onPress={() => searchModalHandler('middlepoints')} color='primary' variant='flat'>
                  <span className='text-ellipsis'>{t('Add way point')}</span>
                </Button>
              </div>
            </div>

            {/* <Timeline items={timelineItems} /> */}

            {/* <Timeline
            pt={{
              content: {
                className: 'mb-5 -mt-1 px-3'
              },
            }}
            value={timelineItems}
            // marker={(item => item.timelabel)}
            content={(item, index) => (
              <div className='sspace-y-2'>
                <div>{item.timelabel}</div>
                <div>{item.body}</div>
              </div>
            )}
            // marker={<div>dd</div>}
          ></Timeline> */}

            <div className='space-y-5 mt-5'>
              {timelineItems.map((item, index) => (
                <div key={index} className='space-y-2'>
                  <div>{item.timelabel}</div>
                  <div>{item.body}</div>
                </div>
              ))}
            </div>

            <div className='mt-10'>
              <Textarea
                variant='flat'
                label={t('Description')}
                labelPlacement='inside'
                placeholder={!isRequest ? t('Enter your description of way') : ''}
                value={description}
                onValueChange={setDescription}
              />
            </div>
            <div className='flex mt-5 justify-end'>
              <Button variant='flat' color='success' isLoading={isLoading} onPress={createCase}>
                {isRequest ? t('Subscribe') : isEdit ? t('Update') : t('Create')}
              </Button>
            </div>
            <Modal backdrop='blur' placement='bottom-center' isOpen={isOpen} onOpenChange={onOpenChange}>
              <ModalContent>
                {(onClose) => (
                  <>
                    <ModalHeader className='flex flex-col gap-1'>{t('Find location')}</ModalHeader>
                    <ModalBody>
                      <div>
                        <MainMap
                          key={activePointType}
                          // mapStyles={{ height: '360px' }}
                          mapClassName='h-[320px]! lg:h-[460px]!'
                          initBrowserGeocode={false}
                          showSearchInput
                          onChangeLocation={onSearchInputChange}
                          // readOnly
                          initZoom={2}
                          // initGeoReverse={initGeoReverse.current && !locationFROM?.lat}
                          initLocationValue={mapLocationValue()}
                          // setMapInstance={setMap}
                          // showSearchInput
                          showFindMe
                        />
                      </div>
                    </ModalBody>
                    <ModalFooter>
                      <Button color='default' variant='light' onPress={onClose}>
                        {t('Close')}
                      </Button>
                    </ModalFooter>
                  </>
                )}
              </ModalContent>
            </Modal>
          </div>
        )}
      </div>
    </ProfileContainer>
  )
}
